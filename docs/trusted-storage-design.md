# Trusty TEE 可信存储新设计方案

## 1. 概述

### 1.0 GP TEE 常量定义

```c
/* GP TEE 对象类型定义 */
#define TEE_TYPE_AES                    0xA0000010
#define TEE_TYPE_DES                    0xA0000011
#define TEE_TYPE_DES3                   0xA0000013
#define TEE_TYPE_HMAC_MD5               0xA0000001
#define TEE_TYPE_HMAC_SHA1              0xA0000002
#define TEE_TYPE_HMAC_SHA224            0xA0000003
#define TEE_TYPE_HMAC_SHA256            0xA0000004
#define TEE_TYPE_HMAC_SHA384            0xA0000005
#define TEE_TYPE_HMAC_SHA512            0xA0000006
#define TEE_TYPE_RSA_PUBLIC_KEY         0xA0000030
#define TEE_TYPE_RSA_KEYPAIR            0xA1000030
#define TEE_TYPE_DSA_PUBLIC_KEY         0xA0000031
#define TEE_TYPE_DSA_KEYPAIR            0xA1000031
#define TEE_TYPE_DH_KEYPAIR             0xA1000032
#define TEE_TYPE_ECDSA_PUBLIC_KEY       0xA0000041
#define TEE_TYPE_ECDSA_KEYPAIR          0xA1000041
#define TEE_TYPE_ECDH_PUBLIC_KEY        0xA0000042
#define TEE_TYPE_ECDH_KEYPAIR           0xA1000042
#define TEE_TYPE_GENERIC_SECRET         0xA0000000

/* GP TEE 属性标识符定义 */
#define TEE_ATTR_SECRET_VALUE           0xC0000000
#define TEE_ATTR_RSA_MODULUS            0xD0000130
#define TEE_ATTR_FLAG_VALUE             0x20000000
#define TEE_ATTR_FLAG_SET               0x1

/* GP TEE 存储标识符定义 */
#define TEE_STORAGE_PRIVATE             0x00000001
#define TEE_STORAGE_PRIVATE_REE         0x80000000

/* GP TEE 数据访问标志定义 */
#define TEE_DATA_FLAG_ACCESS_READ       0x00000001
#define TEE_DATA_FLAG_ACCESS_WRITE      0x00000002
#define TEE_DATA_FLAG_ACCESS_WRITE_META 0x00000004
#define TEE_DATA_FLAG_SHARE_READ        0x00000010
#define TEE_DATA_FLAG_SHARE_WRITE       0x00000020
#define TEE_DATA_FLAG_CREATE            0x00000200
#define TEE_DATA_FLAG_EXCLUSIVE         0x00000400

/* GP TEE 对象使用标志定义 */
#define TEE_USAGE_EXTRACTABLE           0x00000001
#define TEE_USAGE_ENCRYPT               0x00000002
#define TEE_USAGE_DECRYPT               0x00000004
#define TEE_USAGE_MAC                   0x00000008
#define TEE_USAGE_SIGN                  0x00000010
#define TEE_USAGE_VERIFY                0x00000020
#define TEE_USAGE_DERIVE                0x00000040

/* GP TEE 对象 ID 最大长度 */
#define TEE_OBJECT_ID_MAX_LEN           64

/* Trusty 特有常量 */
#define INVALID_IPC_HANDLE              (-1)
#define STORAGE_TA_PORT                 "com.android.trusty.storage"
#define MAX_OBJECTS_PER_TA              256
```

### 1.1 设计目标

本文档详细描述 Trusty TEE 项目中可信存储（trusted storage）的新设计方案，基于 OP-TEE 成熟的双层对象模型，在 Trusty 用户空间环境中实现 GP 标准的可信存储功能。

**核心设计目标：**
- **OP-TEE 架构适配**：完全基于 OP-TEE 的 tee_obj + tee_pobj 双层对象模型
- **用户空间实现**：所有对象管理在用户空间完成，避免内核修改
- **简化设计原则**：保持方案简洁，避免过度复杂化
- **GP 标准兼容**：上层提供完整的 GP 存储 API 接口

### 1.2 适用范围和限制

**适用范围：**
- 支持 GP 标准的瞬时对象（Transient Object）和持久化对象（Persistent Object）
- 适用于单实例 TA 环境下的存储需求
- 支持完整的 GP 存储 API 接口

**设计限制：**
- 多实例支持将在后续版本中单独设计，本文档暂不涉及
- panic 流程将在后续版本中单独设计，本文档暂不涉及
- 当前设计主要针对功能实现，性能优化将在后续迭代中完善

### 1.3 整体架构

基于 OP-TEE 设计原理，采用双层对象模型：

```mermaid
graph TB
    subgraph "GP API Layer"
        A1[TEE_OpenPersistentObject]
        A2[TEE_CreatePersistentObject]
        A3[TEE_AllocateTransientObject]
        A4[TEE_ReadObjectData]
        A5[TEE_WriteObjectData]
    end

    subgraph "TA 用户空间 - tee_obj 管理"
        B1[struct trusty_tee_obj]
        B2[libutee 库管理]
        B3[TA 对象链表]
    end

    subgraph "存储 TA - 简化 tee_pobj 管理"
        C1[struct trusty_pobj<br/>包含 TA 标识字段]
        C2[单链表全局管理]
        C3[直接存储操作]
    end

    subgraph "Trusty 存储服务"
        D1[storage_open_file]
        D2[storage_read/write]
        D3[storage_delete_file]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> C1
    B2 --> B1
    B3 --> B1

    C1 --> D1
    C2 --> C1
    C3 --> C1

    D1 --> D2
    D2 --> D3
```

## 2. 瞬时对象/句柄管理

### 2.1 数据结构设计

基于 OP-TEE 的 tee_obj 设计，但适配 Trusty 用户空间环境。tee_obj 既是瞬时对象又是句柄，由 TA 在用户空间自主管理，无需内核维护链表。

#### 2.1.1 核心数据结构（基于 OP-TEE tee_obj，适配 Trusty）

```c
/* Trusty TEE 对象句柄 - 基于 OP-TEE tee_obj 设计，适配用户空间 */
struct trusty_tee_obj {
    /* 链表管理 - 在 libutee 库中维护 */
    struct list_node link;         /* TA 对象链表节点 */

    /* GP 标准对象信息 - 完全兼容 OP-TEE */
    TEE_ObjectInfo info;           /* GP 标准对象信息 */

    /* 并发控制 - 完全保持 OP-TEE 设计 */
    bool busy;                     /* 操作忙标志，防止并发操作 */

    /* 属性管理 - 完全保持 OP-TEE 设计 */
    uint32_t have_attrs;           /* 属性位字段，标识已设置的属性 */
    void *attr;                    /* 属性数据指针 */

    /* 数据流管理 - 完全保持 OP-TEE 设计 */
    size_t ds_pos;                 /* 数据流起始位置偏移 */

    /* 存储连接 - 适配 Trusty 机制 */
    struct trusty_pobj *pobj;      /* 指向持久化对象的指针 */
    handle_t storage_handle;       /* 存储 TA 连接句柄 */

    /* Trusty 用户空间扩展 */
    mutex_t obj_lock;              /* 对象锁 */
};

/* libutee 库中的对象管理上下文 */
struct utee_object_context {
    struct list_node objects;      /* TA 对象链表头 */
    uint32_t object_count;         /* 当前对象数量 */
    uint32_t max_objects;          /* 最大对象数量限制 */
    mutex_t objects_lock;          /* 对象链表锁 */
};

/* 全局对象管理上下文（在 libutee 库中） */
extern struct utee_object_context g_utee_obj_ctx;
```

#### 2.1.2 OP-TEE vs Trusty 对比

| OP-TEE 字段 | Trusty TEE 字段 | 差异说明 |
|-------------|-----------------|----------|
| `TAILQ_ENTRY(tee_obj) link` | `struct list_node link` | 在 libutee 库中维护链表 |
| `TEE_ObjectInfo info` | `TEE_ObjectInfo info` | 完全保持 GP 标准对象信息 |
| `bool busy` | `bool busy` | 完全保持并发控制标志 |
| `uint32_t have_attrs` | `uint32_t have_attrs` | 完全保持属性位字段 |
| `void *attr` | `void *attr` | 完全保持属性数据指针 |
| `size_t ds_pos` | `size_t ds_pos` | 完全保持数据流位置 |
| `struct tee_pobj *pobj` | `struct trusty_pobj *pobj` | 适配 Trusty 持久对象 |
| `struct tee_file_handle *fh` | `handle_t storage_handle` | 适配 Trusty IPC 句柄 |

#### 2.1.3 Trusty 对象管理模型

```mermaid
graph TB
    subgraph "TA 用户空间"
        A[libutee 库]
        A --> B[对象链表管理]
        B --> C[tee_obj 1]
        B --> D[tee_obj 2]
        B --> E[tee_obj N]
        C --> F[存储 TA 连接]
        D --> G[存储 TA 连接]
        E --> H[存储 TA 连接]
    end

    subgraph "存储 TA"
        I[tee_pobj 全局管理]
        F --> I
        G --> I
        H --> I
    end

    subgraph "TA Panic 处理"
        J[TA Panic]
        J --> K[libutee 对象链表自动清理]
        J --> L[通知存储 TA 清理 tee_pobj]
    end
```

### 2.2 管理机制

TA 通过 libutee 库管理 tee_obj，使用链表数据结构。使用对象地址作为 handle，TA panic 时对象链表自动清理。

#### 2.2.1 对象分配操作（libutee 库实现）

```c
/**
 * 分配新的 tee_obj 对象 - libutee 库实现
 * @return: 成功返回对象指针（作为 handle），失败返回 NULL
 */
struct trusty_tee_obj *utee_obj_alloc(void) {
    struct trusty_tee_obj *obj;

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 检查对象数量限制 */
    if (g_utee_obj_ctx.object_count >= g_utee_obj_ctx.max_objects) {
        mutex_release(&g_utee_obj_ctx.objects_lock);
        return NULL;
    }

    /* 分配对象结构 */
    obj = calloc(1, sizeof(*obj));
    if (!obj) {
        mutex_release(&g_utee_obj_ctx.objects_lock);
        return NULL;
    }

    /* 初始化对象 - 基于 OP-TEE 初始化逻辑 */
    list_initialize(&obj->link);
    memset(&obj->info, 0, sizeof(obj->info));
    obj->busy = false;
    obj->have_attrs = 0;
    obj->attr = NULL;
    obj->ds_pos = 0;
    obj->pobj = NULL;
    obj->storage_handle = INVALID_IPC_HANDLE;
    mutex_init(&obj->obj_lock);

    /* 添加到 libutee 的对象链表 */
    list_add_tail(&g_utee_obj_ctx.objects, &obj->link);
    g_utee_obj_ctx.object_count++;

    mutex_release(&g_utee_obj_ctx.objects_lock);

    /* 返回对象地址作为 handle */
    return obj;
}

/**
 * 释放 tee_obj 对象 - libutee 库实现
 * @param obj: 要释放的对象
 */
void utee_obj_free(struct trusty_tee_obj *obj) {
    if (!obj) {
        return;
    }

    mutex_acquire(&g_utee_obj_ctx.objects_lock);
    mutex_acquire(&obj->obj_lock);

    /* 清理资源 - 基于 OP-TEE 清理逻辑 */
    if (obj->pobj) {
        /* 通知存储 TA 减少持久对象的引用计数 */
        utee_storage_pobj_put(obj->pobj);
        obj->pobj = NULL;
    }

    if (obj->storage_handle != INVALID_IPC_HANDLE) {
        close(obj->storage_handle);
        obj->storage_handle = INVALID_IPC_HANDLE;
    }

    if (obj->attr) {
        /* 安全清除属性数据 */
        memset(obj->attr, 0, obj->have_attrs * sizeof(TEE_Attribute));
        free(obj->attr);
        obj->attr = NULL;
    }

    /* 从链表中移除 */
    list_delete(&obj->link);
    g_utee_obj_ctx.object_count--;

    mutex_release(&obj->obj_lock);
    mutex_destroy(&obj->obj_lock);

    /* 清除对象结构 */
    memset(obj, 0, sizeof(*obj));
    free(obj);

    mutex_release(&g_utee_obj_ctx.objects_lock);
}
```

#### 2.2.2 对象验证操作（libutee 库实现）

```c
/**
 * 验证 handle 是否有效 - libutee 库实现
 * @param handle: 对象 handle（实际是 tee_obj 地址）
 * @return: 成功返回对象指针，失败返回 NULL
 */
struct trusty_tee_obj *utee_obj_get(TEE_ObjectHandle handle) {
    struct trusty_tee_obj *obj = (struct trusty_tee_obj *)handle;
    struct trusty_tee_obj *found_obj = NULL;

    if (!obj) {
        return NULL;
    }

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 在 libutee 的对象链表中查找，验证 handle 有效性 */
    list_for_every_entry(&g_utee_obj_ctx.objects, found_obj,
                         struct trusty_tee_obj, link) {
        if (found_obj == obj) {
            /* handle 有效，返回对象 */
            mutex_release(&g_utee_obj_ctx.objects_lock);
            return obj;
        }
    }

    mutex_release(&g_utee_obj_ctx.objects_lock);
    return NULL;  /* handle 无效 */
}

/**
 * 设置对象忙状态 - 基于 OP-TEE 逻辑
 * @param obj: 对象指针
 * @return: 成功返回 TEE_SUCCESS，失败返回错误码
 */
TEE_Result utee_obj_set_busy(struct trusty_tee_obj *obj) {
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    mutex_acquire(&obj->obj_lock);

    if (obj->busy) {
        mutex_release(&obj->obj_lock);
        return TEE_ERROR_BUSY;
    }

    obj->busy = true;
    mutex_release(&obj->obj_lock);
    return TEE_SUCCESS;
}

/**
 * 清除对象忙状态 - 基于 OP-TEE 逻辑
 * @param obj: 对象指针
 */
void utee_obj_clear_busy(struct trusty_tee_obj *obj) {
    if (!obj) {
        return;
    }

    mutex_acquire(&obj->obj_lock);
    obj->busy = false;
    mutex_release(&obj->obj_lock);
}
```

### 2.3 生命周期管理

在 Trusty 用户空间中，tee_obj 由 libutee 库管理，TA panic 时 libutee 对象链表会自动清理。

#### 2.3.1 TA panic 时的自动清理

```c
/*
 * 重要说明：在 Trusty 用户空间环境中，TA panic 时的处理与 OP-TEE 不同
 *
 * OP-TEE：
 * - tee_obj 在内核中，需要内核主动清理
 * - 内核维护 TA 上下文中的对象链表
 * - panic 时遍历链表清理所有对象
 *
 * Trusty：
 * - tee_obj 在 TA 用户空间的 libutee 库中
 * - TA panic 时，整个用户空间被销毁
 * - libutee 库中的对象链表自动清理
 *
 * 真正需要处理的是：清理该 TA 在存储 TA 中的持久化对象
 */

/**
 * TA panic 时的处理流程 - Trusty 版本
 * @param ta_uuid: panic 的 TA UUID
 */
void handle_ta_panic(const struct uuid *ta_uuid) {
    /*
     * 步骤 1: tee_obj 自动清理
     * - TA 用户空间被销毁时，libutee 库中的所有 tee_obj 自动释放
     * - 包括对象链表、内存、锁等资源
     * - 无需特殊处理
     */

    /*
     * 步骤 2: 通知存储 TA 清理该 TA 的持久化对象
     * - 这是唯一需要主动处理的部分
     */
    notify_storage_ta_cleanup(ta_uuid);
}

/**
 * libutee 库初始化 - 在 TA 启动时调用
 */
void utee_obj_context_init(void) {
    list_initialize(&g_utee_obj_ctx.objects);
    g_utee_obj_ctx.object_count = 0;
    g_utee_obj_ctx.max_objects = MAX_OBJECTS_PER_TA;
    mutex_init(&g_utee_obj_ctx.objects_lock);
}

/**
 * libutee 库清理 - 在 TA 退出时调用（可选）
 */
void utee_obj_context_cleanup(void) {
    struct trusty_tee_obj *obj, *temp;

    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 清理所有剩余对象 */
    list_for_every_entry_safe(&g_utee_obj_ctx.objects, obj, temp,
                              struct trusty_tee_obj, link) {
        utee_obj_free(obj);
    }

    mutex_release(&g_utee_obj_ctx.objects_lock);
    mutex_destroy(&g_utee_obj_ctx.objects_lock);
}
```

## 3. 持久化对象管理

### 3.1 存储 TA 架构分析

基于对 Trusty 存储 TA 代码的深入分析，存储 TA 采用了与传统设计不同的架构。实际的 Trusty 存储 TA 主要负责底层文件系统管理，而不是直接管理 GP 对象。

#### 3.1.1 Trusty 存储 TA 实际架构

**存储 TA 核心组件：**

```c
/* 实际的存储会话结构 - 基于代码分析 */
struct storage_session {
    uint32_t magic;                      /* 会话魔数 STORAGE_SESSION_MAGIC */
    struct block_device_tipc block_device; /* 块设备接口 */
    struct key key;                      /* 存储加密密钥 */
    struct ipc_channel_context proxy_ctx; /* IPC 通信上下文 */
};

/* 客户端会话结构 - 每个连接的 TA 一个 */
struct storage_client_session {
    uint32_t magic;                      /* 会话魔数 STORAGE_CLIENT_SESSION_MAGIC */
    struct transaction tr;               /* 事务上下文 */
    uuid_t uuid;                        /* 客户端 TA UUID */
    struct file_handle** files;         /* 文件句柄数组 */
    size_t files_count;                 /* 文件句柄数量 */
    struct ipc_channel_context context; /* IPC 通信上下文 */
};

/* 文件句柄结构 - 实际的文件操作单元 */
struct file_handle {
    struct list_node node;              /* 事务中的文件链表节点 */
    struct block_mac to_commit_block_mac; /* 待提交的块 MAC */
    struct block_mac committed_block_mac; /* 已提交的块 MAC */
    struct block_mac block_mac;          /* 当前块 MAC */
    data_block_t to_commit_size;        /* 待提交的文件大小 */
    data_block_t size;                  /* 当前文件大小 */
    bool used_by_tr;                    /* 是否被事务使用 */
};

/* 事务结构 - 文件系统事务管理 */
struct transaction {
    struct list_node node;              /* 文件系统事务链表节点 */
    struct fs* fs;                      /* 文件系统状态 */
    struct list_node open_files;        /* 打开的文件链表 */
    bool failed;                        /* 事务失败标志 */
    bool invalid_block_found;           /* 发现无效块标志 */
    bool complete;                      /* 事务完成标志 */
    bool rebuild_free_set;              /* 重建空闲集合标志 */
    bool repaired;                      /* 修复标志 */

    /* 块管理 */
    struct block_set tmp_allocated;     /* 临时分配的块 */
    struct block_set allocated;         /* 已分配的块 */
    struct block_set freed;             /* 已释放的块 */

    /* 文件操作跟踪 */
    struct block_tree files_added;      /* 添加的文件 */
    struct block_tree files_updated;    /* 更新的文件 */
    struct block_tree files_removed;    /* 删除的文件 */
};
```

#### 3.1.2 Trusty 存储 TA 实际架构

```mermaid
graph TB
    subgraph "存储 TA 进程"
        A[main.c - 服务入口]
        A --> B[proxy.c - 代理服务]
        A --> C[client_tipc.c - 客户端处理]

        B --> D[storage_session<br/>代理会话管理]
        C --> E[storage_client_session<br/>客户端会话管理]

        E --> F[transaction<br/>事务管理]
        E --> G[file_handle 数组<br/>文件句柄管理]

        F --> H[file.c - 文件操作]
        F --> I[block_*.c - 块设备管理]

        H --> J[实际文件系统操作]
        I --> K[RPMB/存储后端]
    end

    subgraph "客户端 TA"
        L[GP 存储 API 调用]
        L --> M[TIPC 消息]
        M --> C
    end

    subgraph "会话隔离机制"
        N[每个 TA 独立的 client_session]
        N --> O[基于 TA UUID 的路径隔离]
        N --> P[独立的文件句柄空间]
        N --> Q[独立的事务上下文]
    end
```

#### 3.1.3 存储 TA 关键特性分析

**基于代码分析的关键发现：**

1. **会话隔离机制**：
   - 每个连接的 TA 都有独立的 `storage_client_session`
   - 通过 `uuid_t uuid` 字段标识客户端 TA
   - 每个会话维护独立的文件句柄数组和事务上下文

2. **文件路径隔离**：
   - 使用 `get_path()` 函数基于 TA UUID 生成隔离路径
   - 路径格式：`{ta_uuid}/{filename}`
   - 确保不同 TA 无法访问彼此的文件

3. **事务管理**：
   - 每个客户端会话有独立的事务上下文
   - 支持原子操作和回滚机制
   - 文件操作在事务中进行，确保一致性

4. **文件句柄管理**：
   - 动态分配文件句柄数组
   - 支持最大 `STORAGE_MAX_OPEN_FILES` 个并发文件
   - 自动回收和压缩句柄空间

5. **无需全局对象管理**：
   - 存储 TA 不维护全局的持久对象列表
   - 对象管理通过文件系统路径和文件句柄实现
   - 简化了对象生命周期管理

### 3.2 存储 TA 会话管理机制

基于代码分析，存储 TA 采用会话隔离而非全局对象管理的方式。

#### 3.2.1 会话生命周期管理

**会话创建流程：**

```c
/* 客户端连接时创建会话 - 基于实际代码 */
struct ipc_channel_context* client_connect(struct ipc_port_context* parent_ctx,
                                          const uuid_t* peer_uuid,
                                          handle_t chan_handle) {
    struct storage_client_session* client_session;

    /* 分配客户端会话 */
    client_session = calloc(1, sizeof(*client_session));
    client_session->magic = STORAGE_CLIENT_SESSION_MAGIC;

    /* 初始化文件句柄数组 */
    client_session->files = NULL;
    client_session->files_count = 0;

    /* 初始化事务上下文 */
    transaction_init(&client_session->tr, tr_state, false);

    /* 缓存客户端 TA UUID */
    memcpy(&client_session->uuid, peer_uuid, sizeof(*peer_uuid));

    return &client_session->context;
}
```

**会话清理流程：**

```c
/* 客户端断开时清理会话 - 基于实际代码 */
static void client_disconnect(struct ipc_channel_context* context) {
    struct storage_client_session* session =
        chan_context_to_client_session(context);

    /* 关闭所有打开的文件 */
    session_close_all_files(session);

    /* 清理事务资源 */
    transaction_free(&session->tr);

    /* 释放会话内存 */
    free(session);
}
```

#### 3.2.2 文件句柄管理实现

**动态文件句柄分配：**

```c
/* 创建文件句柄 - 基于实际代码 */
static enum storage_err create_file_handle(
        struct storage_client_session* session,
        uint32_t* handlep,
        struct file_handle** file_p) {
    enum storage_err result;
    uint32_t handle;
    struct file_handle* file;

    /* 查找空闲句柄槽位 */
    for (handle = 0; handle < session->files_count; handle++)
        if (!session->files[handle])
            break;

    /* 需要扩展句柄数组 */
    if (handle >= session->files_count) {
        result = session_set_files_count(session, handle + 1);
        if (result != STORAGE_NO_ERROR)
            return result;
    }

    /* 分配文件句柄 */
    file = calloc(1, sizeof(*file));
    if (!file) {
        return STORAGE_ERR_GENERIC;
    }

    session->files[handle] = file;
    *handlep = handle;
    *file_p = file;
    return STORAGE_NO_ERROR;
}

/* 释放文件句柄 - 基于实际代码 */
static void free_file_handle(struct storage_client_session* session,
                             uint32_t handle) {
    if (handle >= session->files_count || !session->files[handle]) {
        return;
    }

    free(session->files[handle]);
    session->files[handle] = NULL;

    /* 压缩句柄数组 */
    session_shrink_files(session);
}
```

#### 3.2.3 路径隔离机制

**基于 TA UUID 的路径生成：**

```c
/* 路径生成函数 - 基于实际代码 */
static int get_path(char* path_out,
                    size_t path_out_size,
                    const uuid_t* uuid,
                    const char* file_name,
                    size_t file_name_len) {
    unsigned int rc;

    /* 生成基于 TA UUID 的路径前缀 */
    rc = snprintf(path_out, path_out_size,
                  "%08x%04x%04x%02x%02x%02x%02x%02x%02x%02x%02x/",
                  uuid->time_low, uuid->time_mid, uuid->time_hi_and_version,
                  uuid->clock_seq_and_node[0], uuid->clock_seq_and_node[1],
                  uuid->clock_seq_and_node[2], uuid->clock_seq_and_node[3],
                  uuid->clock_seq_and_node[4], uuid->clock_seq_and_node[5],
                  uuid->clock_seq_and_node[6], uuid->clock_seq_and_node[7]);

    if (rc + file_name_len >= path_out_size) {
        return STORAGE_ERR_NOT_VALID;
    }

    /* 追加文件名 */
    memcpy(path_out + rc, file_name, file_name_len);
    path_out[rc + file_name_len] = '\0';

    return STORAGE_NO_ERROR;
}
```

**文件名验证：**

```c
/* 文件名合法性检查 - 基于实际代码 */
static int is_valid_name(const char* name, size_t name_len) {
    size_t i;

    if (!name_len)
        return 0;

    /* 只允许 [a-z][A-Z][0-9][.-_] 字符 */
    for (i = 0; i < name_len; i++) {
        if ((name[i] >= 'a') && (name[i] <= 'z'))
            continue;
        if ((name[i] >= 'A') && (name[i] <= 'Z'))
            continue;
        if ((name[i] >= '0') && (name[i] <= '9'))
            continue;
        if ((name[i] == '.') || (name[i] == '-') || (name[i] == '_'))
            continue;

        return 0;  /* 非法字符 */
    }

    return 1;
}
```

### 3.3 存储操作实现

基于代码分析，存储 TA 通过文件系统接口提供存储服务，而不是直接管理对象。

#### 3.3.1 文件操作接口

**文件打开操作：**

```c
/* 文件打开 - 基于实际代码 */
static int storage_file_open(struct storage_msg* msg,
                             struct storage_file_open_req* req,
                             size_t req_size,
                             struct storage_client_session* session) {
    enum file_op_result open_result;
    enum storage_err result;
    struct file_handle* file = NULL;
    const char* fname;
    size_t fname_len;
    uint32_t flags, f_handle;
    char path_buf[FS_PATH_MAX];
    enum file_create_mode file_create_mode;

    /* 验证请求参数 */
    if (req_size < sizeof(*req)) {
        return STORAGE_ERR_NOT_VALID;
    }

    flags = req->flags;
    fname = req->name;
    fname_len = req_size - sizeof(*req);

    /* 验证文件名合法性 */
    if (!is_valid_name(fname, fname_len)) {
        return STORAGE_ERR_NOT_VALID;
    }

    /* 生成隔离路径 */
    result = get_path(path_buf, sizeof(path_buf), &session->uuid, fname, fname_len);
    if (result != STORAGE_NO_ERROR) {
        return result;
    }

    /* 创建文件句柄 */
    result = create_file_handle(session, &f_handle, &file);
    if (result != STORAGE_NO_ERROR) {
        return result;
    }

    /* 确定文件创建模式 */
    if (flags & STORAGE_FILE_OPEN_CREATE) {
        if (flags & STORAGE_FILE_OPEN_CREATE_EXCLUSIVE) {
            file_create_mode = FILE_OPEN_CREATE_EXCLUSIVE;
        } else {
            file_create_mode = FILE_OPEN_CREATE;
        }
    } else {
        file_create_mode = FILE_OPEN_NO_CREATE;
    }

    /* 执行文件打开操作 */
    open_result = file_open(&session->tr, path_buf, file, file_create_mode,
                           msg->flags & STORAGE_MSG_FLAG_FS_REPAIRED_ACK);

    if (open_result != FILE_OP_SUCCESS) {
        free_file_handle(session, f_handle);
        return file_op_result_to_storage_err(open_result);
    }

    /* 返回文件句柄 */
    return send_response(session, STORAGE_NO_ERROR, msg, &f_handle, sizeof(f_handle));
}
```

#### 3.3.2 事务管理机制

**事务初始化和管理：**

```c
/* 事务初始化 - 基于实际代码 */
void transaction_init(struct transaction* tr, struct fs* fs, bool activate) {
    memset(tr, 0, sizeof(*tr));
    list_initialize(&tr->node);
    list_initialize(&tr->open_files);
    tr->fs = fs;

    if (activate) {
        transaction_activate(tr);
    }
}

/* 事务激活 - 基于实际代码 */
void transaction_activate(struct transaction* tr) {
    assert(tr->fs);
    assert(!transaction_is_active(tr));

    tr->failed = false;
    tr->invalid_block_found = false;
    tr->complete = false;
    tr->rebuild_free_set = false;
    tr->repaired = false;

    /* 初始化块集合 */
    block_set_init(tr->fs, &tr->tmp_allocated);
    block_set_init(tr->fs, &tr->allocated);
    block_set_init(tr->fs, &tr->freed);

    /* 初始化文件树 */
    fs_file_tree_init(tr->fs, &tr->files_added);
    fs_file_tree_init(tr->fs, &tr->files_updated);
    fs_file_tree_init(tr->fs, &tr->files_removed);

    /* 添加到活动事务列表 */
    list_add_tail(&tr->fs->allocated, &tr->allocated.node);
    list_add_tail(&tr->fs->allocated, &tr->tmp_allocated.node);
}

/* 事务提交 - 基于实际代码 */
void transaction_complete_etc(struct transaction* tr, bool update_checkpoint) {
    if (tr->failed) {
        return;
    }

    /* 执行事务提交逻辑 */
    /* ... 复杂的文件系统事务提交过程 ... */

    tr->complete = true;
}
```

#### 3.3.3 TA Panic 处理机制

**基于会话的自动清理：**

```c
/* TA 断开连接时的自动清理 - 基于实际代码 */
static void client_disconnect(struct ipc_channel_context* context) {
    struct storage_client_session* session =
        chan_context_to_client_session(context);

    /* 自动关闭所有打开的文件 */
    session_close_all_files(session);

    /* 清理事务资源 */
    transaction_free(&session->tr);

    /* 释放会话内存 */
    free(session);
}

/* 关闭所有文件 - 基于实际代码 */
static void session_close_all_files(struct storage_client_session* session) {
    uint32_t f_handle;
    struct file_handle* file;

    for (f_handle = 0; f_handle < session->files_count; f_handle++) {
        file = session->files[f_handle];
        if (file) {
            file_close(file);  /* 关闭文件，释放文件系统资源 */
            free(file);        /* 释放文件句柄内存 */
        }
    }

    if (session->files) {
        free(session->files);  /* 释放文件句柄数组 */
    }
    session->files_count = 0;
}
```

**关键设计特点：**

1. **无需全局对象清理**：TA panic 时，对应的客户端会话自动断开
2. **自动资源回收**：会话断开时自动关闭所有文件句柄和事务
3. **文件系统一致性**：通过事务机制确保文件系统状态一致
4. **简化的清理逻辑**：不需要遍历全局对象列表进行清理

## 4. 设计总结

### 4.1 Trusty 存储 TA 架构特点

基于对实际代码的深入分析，Trusty 存储 TA 的设计具有以下特点：

#### 4.1.1 会话隔离架构

**核心设计原则：**
- **会话隔离**：每个连接的 TA 都有独立的客户端会话
- **路径隔离**：基于 TA UUID 生成隔离的文件路径
- **资源隔离**：每个会话维护独立的文件句柄和事务上下文
- **自动清理**：TA 断开时自动清理所有相关资源

#### 4.1.2 与传统设计的差异

**传统 OP-TEE 设计：**
- 全局持久对象管理
- 复杂的对象引用计数
- 需要主动的 panic 清理机制

**Trusty 存储 TA 设计：**
- 基于会话的资源管理
- 文件系统级别的隔离
- 自动的资源清理机制
- 简化的对象生命周期

### 4.2 对 GP 存储设计的影响

基于 Trusty 存储 TA 的实际架构，需要重新考虑 GP 存储的设计策略。

#### 4.2.1 设计建议

**推荐的 GP 存储架构：**

1. **用户空间对象管理**：
   - 在 libutee 库中实现完整的 GP 对象抽象
   - 瞬态对象完全在用户空间管理
   - 持久对象通过存储 TA 的文件接口实现

2. **简化的存储后端**：
   - 直接使用 Trusty 存储 TA 的文件接口
   - 利用现有的路径隔离和会话管理机制
   - 无需实现复杂的对象引用计数

3. **自动资源管理**：
   - 利用存储 TA 的自动清理机制
   - TA panic 时无需特殊的对象清理逻辑
   - 简化错误处理和资源回收

#### 4.2.2 实现优势

**相比传统 OP-TEE 设计的优势：**

1. **简化的架构**：无需复杂的全局对象管理
2. **更好的隔离**：基于文件系统的天然隔离
3. **自动清理**：利用会话断开的自动清理机制
4. **更好的可维护性**：减少了状态管理的复杂性

**需要注意的限制：**

1. **性能考虑**：每个对象操作都需要 TIPC 通信
2. **并发限制**：受存储 TA 的文件句柄数量限制
3. **兼容性**：需要确保与 GP 标准的完全兼容

#### 4.2.3 GP API 到存储 TA 的映射

**持久对象操作映射：**

| GP API | 存储 TA 操作 | 实现说明 |
|--------|-------------|----------|
| `TEE_CreatePersistentObject` | `storage_file_open` + CREATE | 创建文件并返回句柄 |
| `TEE_OpenPersistentObject` | `storage_file_open` | 打开现有文件 |
| `TEE_CloseObject` | `storage_file_close` | 关闭文件句柄 |
| `TEE_ReadObjectData` | `storage_file_read` | 读取文件数据 |
| `TEE_WriteObjectData` | `storage_file_write` | 写入文件数据 |
| `TEE_TruncateObjectData` | `storage_file_set_size` | 设置文件大小 |
| `TEE_RenamePersistentObject` | `storage_file_move` | 重命名文件 |

**瞬态对象操作：**
- 完全在用户空间 libutee 中实现
- 不涉及存储 TA 交互
- 使用内存管理和链表维护

## 5. 总结

### 5.1 关键发现

通过对 Trusty 存储 TA 代码的深入分析，发现了与传统 OP-TEE 设计的重要差异：

1. **会话隔离架构**：Trusty 存储 TA 采用基于会话的隔离机制，而非全局对象管理
2. **自动资源清理**：TA 断开时自动清理所有相关资源，无需复杂的 panic 处理
3. **文件系统抽象**：通过文件系统接口提供存储服务，简化了对象管理
4. **路径隔离**：基于 TA UUID 的路径隔离确保安全性

### 5.2 设计建议

基于分析结果，对 GP 存储设计的建议：

1. **采用用户空间对象管理**：在 libutee 库中实现 GP 对象抽象
2. **利用现有存储架构**：直接使用 Trusty 存储 TA 的文件接口
3. **简化资源管理**：利用会话断开的自动清理机制
4. **确保 GP 兼容性**：在简化架构的基础上保持与 GP 标准的完全兼容

### 5.3 后续工作

1. **完善 GP 存储 API 实现**：基于分析结果完善用户空间的 GP 存储 API
2. **性能优化**：针对 TIPC 通信开销进行优化
3. **测试验证**：确保与 GP 标准的完全兼容性
4. **文档更新**：更新相关设计文档以反映实际架构

























#### 6.2.2 libutee 库核心接口实现

**核心存储操作函数：**
- `utee_storage_open_object()` - 打开持久对象
- `utee_storage_create_object()` - 创建持久对象
- `utee_storage_read_data()` - 读取对象数据
- `utee_storage_write_data()` - 写入对象数据
- `utee_storage_delete_object()` - 删除持久对象

**辅助函数：**
- `get_current_ta_uuid()` - 获取当前 TA UUID
- `get_current_ta_instance_id()` - 获取当前 TA 实例号
- `utee_storage_pobj_put()` - 减少持久对象引用计数

```c
/**
 * 打开持久对象 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param ta_uuid: TA UUID
 * @param instance_id: TA 实例号
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @param flags: 访问标志
 * @param pobj: 输出的持久对象引用
 * @return: TEE_Result
 */
TEE_Result utee_storage_open_object(handle_t storage_handle,
                                   const struct uuid *ta_uuid,
                                   uint32_t instance_id,
                                   const void *obj_id, uint32_t obj_id_len,
                                   uint32_t flags,
                                   struct trusty_pobj **pobj) {
    struct storage_open_req req;
    struct storage_open_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_OPEN_OBJECT;
    memcpy(&req.ta_uuid, ta_uuid, sizeof(*ta_uuid));
    req.instance_id = instance_id;
    req.storage_id = TEE_STORAGE_PRIVATE;  /* 默认私有存储 */
    req.obj_id_len = obj_id_len;
    req.flags = flags;

    /* 发送请求 */
    msg.num_iov = 2;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.iov[1].iov_base = (void *)obj_id;
    msg.iov[1].iov_len = obj_id_len;
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &resp;
    msg.iov[0].iov_len = sizeof(resp);
    msg.num_handles = 0;

    ret = read_msg(storage_handle, msg.iov[0].iov_base, msg.iov[0].iov_len);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    if (resp.result != TEE_SUCCESS) {
        return resp.result;
    }

    /* 创建本地 pobj 引用 */
    *pobj = (struct trusty_pobj *)resp.pobj_handle;
    return TEE_SUCCESS;
}

/**
 * 创建持久对象 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param ta_uuid: TA UUID
 * @param instance_id: TA 实例号
 * @param obj_id: 对象 ID
 * @param obj_id_len: 对象 ID 长度
 * @param flags: 访问标志
 * @param initial_data: 初始数据
 * @param initial_data_len: 初始数据长度
 * @param pobj: 输出的持久对象引用
 * @return: TEE_Result
 */
TEE_Result utee_storage_create_object(handle_t storage_handle,
                                     const struct uuid *ta_uuid,
                                     uint32_t instance_id,
                                     const void *obj_id, uint32_t obj_id_len,
                                     uint32_t flags,
                                     const void *initial_data, uint32_t initial_data_len,
                                     struct trusty_pobj **pobj) {
    struct storage_create_req req;
    struct storage_create_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_CREATE_OBJECT;
    memcpy(&req.ta_uuid, ta_uuid, sizeof(*ta_uuid));
    req.instance_id = instance_id;
    req.storage_id = TEE_STORAGE_PRIVATE;  /* 默认私有存储 */
    req.obj_id_len = obj_id_len;
    req.flags = flags;
    req.initial_data_len = initial_data_len;

    /* 发送请求 */
    msg.num_iov = 3;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.iov[1].iov_base = (void *)obj_id;
    msg.iov[1].iov_len = obj_id_len;
    msg.iov[2].iov_base = (void *)initial_data;
    msg.iov[2].iov_len = initial_data_len;
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &resp;
    msg.iov[0].iov_len = sizeof(resp);
    msg.num_handles = 0;

    ret = read_msg(storage_handle, msg.iov[0].iov_base, msg.iov[0].iov_len);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    if (resp.result != TEE_SUCCESS) {
        return resp.result;
    }

    /* 创建本地 pobj 引用 */
    *pobj = (struct trusty_pobj *)resp.pobj_handle;
    return TEE_SUCCESS;
}

/**
 * 读取对象数据 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param pobj: 持久对象引用
 * @param offset: 读取偏移
 * @param buffer: 数据缓冲区
 * @param size: 读取大小
 * @param bytes_read: 实际读取字节数
 * @return: TEE_Result
 */
TEE_Result utee_storage_read_data(handle_t storage_handle,
                                 struct trusty_pobj *pobj,
                                 uint32_t offset, void *buffer,
                                 uint32_t size, uint32_t *bytes_read) {
    struct storage_read_req req;
    struct storage_read_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_READ_DATA;
    req.pobj_handle = (uint64_t)pobj;
    req.offset = offset;
    req.size = size;

    /* 发送请求 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应头 */
    ret = read_msg(storage_handle, &resp, sizeof(resp));
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    if (resp.result != TEE_SUCCESS) {
        return resp.result;
    }

    /* 接收数据内容 */
    if (resp.bytes_read > 0) {
        ret = read_msg(storage_handle, buffer, resp.bytes_read);
        if (ret < 0) {
            return TEE_ERROR_COMMUNICATION;
        }
    }

    *bytes_read = resp.bytes_read;
    return TEE_SUCCESS;
}

/**
 * 写入对象数据 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param pobj: 持久对象引用
 * @param offset: 写入偏移
 * @param buffer: 数据缓冲区
 * @param size: 写入大小
 * @return: TEE_Result
 */
TEE_Result utee_storage_write_data(handle_t storage_handle,
                                  struct trusty_pobj *pobj,
                                  uint32_t offset, const void *buffer,
                                  uint32_t size) {
    struct storage_write_req req;
    struct storage_write_resp resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_WRITE_DATA;
    req.pobj_handle = (uint64_t)pobj;
    req.offset = offset;
    req.size = size;

    /* 发送请求 */
    msg.num_iov = 2;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.iov[1].iov_base = (void *)buffer;
    msg.iov[1].iov_len = size;
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    ret = read_msg(storage_handle, &resp, sizeof(resp));
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    return resp.result;
}

/**
 * 删除持久对象 - libutee 库实现
 * @param storage_handle: 存储 TA 连接句柄
 * @param pobj: 持久对象引用
 * @return: TEE_Result
 */
TEE_Result utee_storage_delete_object(handle_t storage_handle,
                                     struct trusty_pobj *pobj) {
    struct storage_delete_req {
        uint32_t cmd;
        uint64_t pobj_handle;
    } req;
    struct storage_delete_resp {
        TEE_Result result;
    } resp;
    ipc_msg_t msg;
    int ret;

    /* 准备请求消息 */
    req.cmd = STORAGE_CMD_DELETE_OBJECT;
    req.pobj_handle = (uint64_t)pobj;

    /* 发送请求 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = &req;
    msg.iov[0].iov_len = sizeof(req);
    msg.num_handles = 0;

    ret = send_msg(storage_handle, &msg);
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    /* 接收响应 */
    ret = read_msg(storage_handle, &resp, sizeof(resp));
    if (ret < 0) {
        return TEE_ERROR_COMMUNICATION;
    }

    return resp.result;
}

/**
 * 减少持久对象引用计数 - libutee 库实现
 * @param pobj: 持久对象引用
 */
void utee_storage_pobj_put(struct trusty_pobj *pobj) {
    /* 在实际实现中，这里应该通过 IPC 通知存储 TA 减少引用计数 */
    /* 为了简化，这里只是一个占位符 */
    (void)pobj;
}

/**
 * 获取当前 TA 实例号 - libutee 库实现
 * @return: 当前 TA 的实例号
 */
uint32_t get_current_ta_instance_id(void) {
    /*
     * 在实际实现中，这个函数应该：
     * 1. 从 Trusty 内核获取当前 TA 的实例号
     * 2. 或者从 TA 启动时传入的参数中获取
     * 3. 或者从全局变量中获取（在 TA 初始化时设置）
     *
     * 这里返回一个示例值，实际实现需要根据 Trusty 的具体机制
     */
    extern uint32_t g_current_ta_instance_id;  /* 全局实例号变量 */
    return g_current_ta_instance_id;
}

/**
 * 获取当前 TA UUID - libutee 库实现
 * @param ta_uuid: 输出的 TA UUID
 */
void get_current_ta_uuid(struct uuid *ta_uuid) {
    /*
     * 在实际实现中，这个函数应该：
     * 1. 从 Trusty 内核获取当前 TA 的 UUID
     * 2. 或者从 TA 的 manifest 文件中获取
     * 3. 或者从全局变量中获取（在 TA 初始化时设置）
     */
    extern struct uuid g_current_ta_uuid;  /* 全局 UUID 变量 */
    if (ta_uuid) {
        memcpy(ta_uuid, &g_current_ta_uuid, sizeof(*ta_uuid));
    }
}

/**
 * 验证对象类型是否有效 - libutee 库实现
 * @param objectType: 对象类型
 * @return: 有效返回 true，无效返回 false
 */
bool utee_is_valid_object_type(uint32_t objectType) {
    switch (objectType) {
        case TEE_TYPE_AES:
        case TEE_TYPE_DES:
        case TEE_TYPE_DES3:
        case TEE_TYPE_HMAC_MD5:
        case TEE_TYPE_HMAC_SHA1:
        case TEE_TYPE_HMAC_SHA224:
        case TEE_TYPE_HMAC_SHA256:
        case TEE_TYPE_HMAC_SHA384:
        case TEE_TYPE_HMAC_SHA512:
        case TEE_TYPE_RSA_PUBLIC_KEY:
        case TEE_TYPE_RSA_KEYPAIR:
        case TEE_TYPE_DSA_PUBLIC_KEY:
        case TEE_TYPE_DSA_KEYPAIR:
        case TEE_TYPE_DH_KEYPAIR:
        case TEE_TYPE_ECDSA_PUBLIC_KEY:
        case TEE_TYPE_ECDSA_KEYPAIR:
        case TEE_TYPE_ECDH_PUBLIC_KEY:
        case TEE_TYPE_ECDH_KEYPAIR:
        case TEE_TYPE_GENERIC_SECRET:
            return true;
        default:
            return false;
    }
}

/**
 * 验证存储 ID 是否有效 - libutee 库实现
 * @param storageID: 存储 ID
 * @return: 有效返回 true，无效返回 false
 */
bool utee_is_valid_storage_id(uint32_t storageID) {
    switch (storageID) {
        case TEE_STORAGE_PRIVATE:
        case TEE_STORAGE_PRIVATE_REE:
            return true;
        default:
            return false;
    }
}

/**
 * 验证访问标志是否有效 - libutee 库实现
 * @param flags: 访问标志
 * @return: 有效返回 true，无效返回 false
 */
bool utee_is_valid_access_flags(uint32_t flags) {
    uint32_t valid_flags = TEE_DATA_FLAG_ACCESS_READ |
                          TEE_DATA_FLAG_ACCESS_WRITE |
                          TEE_DATA_FLAG_ACCESS_WRITE_META |
                          TEE_DATA_FLAG_SHARE_READ |
                          TEE_DATA_FLAG_SHARE_WRITE |
                          TEE_DATA_FLAG_CREATE |
                          TEE_DATA_FLAG_EXCLUSIVE;

    /* 检查是否有无效标志 */
    if (flags & ~valid_flags) {
        return false;
    }

    /* 至少需要一个访问权限 */
    if (!(flags & (TEE_DATA_FLAG_ACCESS_READ | TEE_DATA_FLAG_ACCESS_WRITE))) {
        return false;
    }

    return true;
}

/**
 * 复制对象属性 - libutee 库实现
 * @param obj: 目标对象
 * @param attrs: 源属性数组
 * @param attrCount: 属性数量
 * @return: 成功返回 TEE_SUCCESS，失败返回错误码
 */
TEE_Result utee_obj_copy_attributes(struct trusty_tee_obj *obj,
                                   const TEE_Attribute *attrs,
                                   uint32_t attrCount) {
    if (!obj || (!attrs && attrCount > 0)) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 清理现有属性 */
    if (obj->attr) {
        secure_memset(obj->attr, 0, obj->have_attrs * sizeof(TEE_Attribute));
        free(obj->attr);
        obj->attr = NULL;
        obj->have_attrs = 0;
    }

    if (attrCount == 0) {
        return TEE_SUCCESS;
    }

    /* 分配新的属性数组 */
    obj->attr = malloc(attrCount * sizeof(TEE_Attribute));
    if (!obj->attr) {
        return TEE_ERROR_OUT_OF_MEMORY;
    }

    /* 复制属性 */
    memcpy(obj->attr, attrs, attrCount * sizeof(TEE_Attribute));
    obj->have_attrs = attrCount;

    return TEE_SUCCESS;
}

/**
 * 计算对象大小 - libutee 库实现
 * @param obj: 对象指针
 * @return: 对象大小（字节）
 */
uint32_t utee_obj_calculate_size(struct trusty_tee_obj *obj) {
    uint32_t size = 0;
    uint32_t i;

    if (!obj || !obj->attr) {
        return 0;
    }

    /* 根据对象类型和属性计算大小 */
    switch (obj->info.objectType) {
        case TEE_TYPE_AES:
            /* AES 密钥大小由 SECRET_VALUE 属性决定 */
            for (i = 0; i < obj->have_attrs; i++) {
                TEE_Attribute *attr = &((TEE_Attribute *)obj->attr)[i];
                if (attr->attributeID == TEE_ATTR_SECRET_VALUE) {
                    if (attr->attributeID & TEE_ATTR_FLAG_VALUE) {
                        size = 32;  /* 默认 256 位 */
                    } else {
                        size = attr->content.ref.length * 8;  /* 转换为位 */
                    }
                    break;
                }
            }
            break;

        case TEE_TYPE_RSA_PUBLIC_KEY:
        case TEE_TYPE_RSA_KEYPAIR:
            /* RSA 密钥大小由模数长度决定 */
            for (i = 0; i < obj->have_attrs; i++) {
                TEE_Attribute *attr = &((TEE_Attribute *)obj->attr)[i];
                if (attr->attributeID == TEE_ATTR_RSA_MODULUS) {
                    if (!(attr->attributeID & TEE_ATTR_FLAG_VALUE)) {
                        size = attr->content.ref.length * 8;  /* 转换为位 */
                    }
                    break;
                }
            }
            break;

        case TEE_TYPE_GENERIC_SECRET:
            /* 通用密钥大小由 SECRET_VALUE 属性决定 */
            for (i = 0; i < obj->have_attrs; i++) {
                TEE_Attribute *attr = &((TEE_Attribute *)obj->attr)[i];
                if (attr->attributeID == TEE_ATTR_SECRET_VALUE) {
                    if (!(attr->attributeID & TEE_ATTR_FLAG_VALUE)) {
                        size = attr->content.ref.length * 8;  /* 转换为位 */
                    }
                    break;
                }
            }
            break;

        default:
            size = 0;
            break;
    }

    return size;
}

/**
 * 增加持久对象引用计数 - libutee 库实现
 * @param pobj: 持久对象引用
 * @return: 成功返回 true，失败返回 false
 */
bool utee_storage_pobj_get(struct trusty_pobj *pobj) {
    /* 在实际实现中，这里应该通过 IPC 通知存储 TA 增加引用计数 */
    /* 为了简化，这里只是一个占位符 */
    (void)pobj;
    return true;
}

/**
 * 获取当前时间戳 - 系统辅助函数
 * @return: 当前时间戳（纳秒）
 */
uint64_t current_time_ns(void) {
    /*
     * 在实际实现中，这个函数应该：
     * 1. 调用 Trusty 系统调用获取当前时间
     * 2. 或者使用 clock_gettime() 等标准函数
     * 3. 返回纳秒级时间戳
     */
    struct timespec ts;
    if (clock_gettime(CLOCK_MONOTONIC, &ts) == 0) {
        return (uint64_t)ts.tv_sec * 1000000000ULL + (uint64_t)ts.tv_nsec;
    }
    return 0;  /* 错误情况下返回 0 */
}

/**
 * 安全内存清除 - 防止编译器优化
 * @param ptr: 内存指针
 * @param value: 填充值
 * @param size: 内存大小
 */
void secure_memset(void *ptr, int value, size_t size) {
    if (!ptr || size == 0) {
        return;
    }

    volatile uint8_t *p = (volatile uint8_t *)ptr;
    while (size--) {
        *p++ = (uint8_t)value;
    }

    /* 内存屏障，确保编译器不会优化掉这个操作 */
    __asm__ __volatile__("" ::: "memory");
}
```

### 6.3 GP API 属性管理接口

#### 6.3.1 属性操作辅助函数

```c
/**
 * TEE_GetObjectInfo1 - 获取对象信息
 * @param object: 对象句柄
 * @param objectInfo: 输出的对象信息
 * @return: TEE_Result
 */
TEE_Result TEE_GetObjectInfo1(TEE_ObjectHandle object, TEE_ObjectInfo *objectInfo) {
    struct trusty_tee_obj *obj;

    if (!objectInfo) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 复制对象信息 */
    memcpy(objectInfo, &obj->info, sizeof(TEE_ObjectInfo));
    return TEE_SUCCESS;
}

/**
 * TEE_RestrictObjectUsage1 - 限制对象使用权限
 * @param object: 对象句柄
 * @param objectUsage: 新的使用权限
 * @return: TEE_Result
 */
TEE_Result TEE_RestrictObjectUsage1(TEE_ObjectHandle object, uint32_t objectUsage) {
    struct trusty_tee_obj *obj;
    TEE_Result res;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return TEE_ERROR_BAD_PARAMETERS;
    }

    /* 设置 busy 状态 */
    res = utee_obj_set_busy(obj);
    if (res != TEE_SUCCESS) {
        return res;
    }

    /* 只能限制权限，不能扩展权限 */
    obj->info.objectUsage &= objectUsage;

    /* 清除 busy 状态 */
    utee_obj_clear_busy(obj);
    return TEE_SUCCESS;
}

/**
 * TEE_GetObjectBufferAttribute - 获取缓冲区属性
 * @param object: 对象句柄
 * @param attributeID: 属性 ID
 * @param buffer: 输出缓冲区
 * @param size: 缓冲区大小指针
 * @return: 属性标志
 */
uint32_t TEE_GetObjectBufferAttribute(TEE_ObjectHandle object, uint32_t attributeID,
                                     void *buffer, uint32_t *size) {
    struct trusty_tee_obj *obj;
    TEE_Attribute *attr;
    uint32_t i;

    if (!size) {
        return 0;
    }

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return 0;
    }

    /* 查找属性 */
    for (i = 0; i < obj->have_attrs; i++) {
        attr = &((TEE_Attribute *)obj->attr)[i];
        if (attr->attributeID == attributeID) {
            /* 检查是否为缓冲区属性 */
            if (!(attr->attributeID & TEE_ATTR_FLAG_VALUE)) {
                uint32_t attr_size = attr->content.ref.length;

                if (buffer && *size >= attr_size) {
                    memcpy(buffer, attr->content.ref.buffer, attr_size);
                }
                *size = attr_size;
                return TEE_ATTR_FLAG_SET;
            }
        }
    }

    return 0;
}

/**
 * TEE_GetObjectValueAttribute - 获取值属性
 * @param object: 对象句柄
 * @param attributeID: 属性 ID
 * @param a: 输出值 A
 * @param b: 输出值 B
 * @return: 属性标志
 */
uint32_t TEE_GetObjectValueAttribute(TEE_ObjectHandle object, uint32_t attributeID,
                                    uint32_t *a, uint32_t *b) {
    struct trusty_tee_obj *obj;
    TEE_Attribute *attr;
    uint32_t i;

    /* 验证 handle */
    obj = utee_obj_get(object);
    if (!obj) {
        return 0;
    }

    /* 查找属性 */
    for (i = 0; i < obj->have_attrs; i++) {
        attr = &((TEE_Attribute *)obj->attr)[i];
        if (attr->attributeID == attributeID) {
            /* 检查是否为值属性 */
            if (attr->attributeID & TEE_ATTR_FLAG_VALUE) {
                if (a) *a = attr->content.value.a;
                if (b) *b = attr->content.value.b;
                return TEE_ATTR_FLAG_SET;
            }
        }
    }

    return 0;
}

/**
 * TEE_InitRefAttribute - 初始化引用属性
 * @param attr: 属性结构
 * @param attributeID: 属性 ID
 * @param buffer: 数据缓冲区
 * @param length: 数据长度
 */
void TEE_InitRefAttribute(TEE_Attribute *attr, uint32_t attributeID,
                         const void *buffer, uint32_t length) {
    if (!attr) {
        return;
    }

    attr->attributeID = attributeID;
    attr->content.ref.buffer = (void *)buffer;
    attr->content.ref.length = length;
}

/**
 * TEE_InitValueAttribute - 初始化值属性
 * @param attr: 属性结构
 * @param attributeID: 属性 ID
 * @param a: 值 A
 * @param b: 值 B
 */
void TEE_InitValueAttribute(TEE_Attribute *attr, uint32_t attributeID,
                           uint32_t a, uint32_t b) {
    if (!attr) {
        return;
    }

    attr->attributeID = attributeID | TEE_ATTR_FLAG_VALUE;
    attr->content.value.a = a;
    attr->content.value.b = b;
}

/**
 * TEE_CopyObjectAttributes1 - 复制对象属性
 * @param destObject: 目标对象句柄
 * @param srcObject: 源对象句柄
 */
void TEE_CopyObjectAttributes1(TEE_ObjectHandle destObject, TEE_ObjectHandle srcObject) {
    struct trusty_tee_obj *dest_obj, *src_obj;
    TEE_Result res;

    /* 验证 handle */
    dest_obj = utee_obj_get(destObject);
    src_obj = utee_obj_get(srcObject);
    if (!dest_obj || !src_obj) {
        return;
    }

    /* 检查是否为瞬时对象 */
    if (dest_obj->pobj || src_obj->pobj) {
        return;
    }

    /* 设置目标对象 busy 状态 */
    res = utee_obj_set_busy(dest_obj);
    if (res != TEE_SUCCESS) {
        return;
    }

    /* 复制属性 */
    if (src_obj->attr && src_obj->have_attrs > 0) {
        utee_obj_copy_attributes(dest_obj, (TEE_Attribute *)src_obj->attr, src_obj->have_attrs);
    }

    /* 复制对象信息 */
    dest_obj->info.objectType = src_obj->info.objectType;
    dest_obj->info.objectSize = src_obj->info.objectSize;
    dest_obj->info.maxObjectSize = src_obj->info.maxObjectSize;
    dest_obj->info.objectUsage = src_obj->info.objectUsage;

    /* 清除 busy 状态 */
    utee_obj_clear_busy(dest_obj);
}
```

## 7. 实现注意事项

### 7.1 关键技术点

在实现该设计方案时，需要特别注意以下关键技术点，确保系统的正确性和可靠性。

#### 7.1.1 瞬时对象 busy 状态的必要性

**核心结论：瞬时对象在用户层 TA 管理后仍然需要 busy 状态机制。**

**技术原因分析：**

1. **GP 标准合规性**：GP TEE Internal Core API 明确要求对象操作的原子性保护
2. **多线程安全**：即使在用户空间，TA 可能存在多线程并发访问同一对象
3. **操作一致性**：确保属性修改、数据操作等复合操作的原子性
4. **OP-TEE 兼容性**：保持与 OP-TEE 完全一致的并发控制语义

**具体使用场景：**
- 属性填充操作期间防止并发修改
- 对象信息更新期间防止并发访问
- 数据流操作期间防止状态不一致
- 对象重置操作期间防止并发干扰

#### 7.1.2 OP-TEE vs Trusty 的关键差异

**OP-TEE 模型：**
- tee_obj 和 tee_pobj 都在内核中
- 内核维护 TA 上下文中的对象链表
- TA panic 时需要内核主动清理所有对象

**Trusty 模型：**
- tee_obj 在 TA 用户空间的 libutee 库中
- tee_pobj 在存储 TA 中，统一管理
- TA panic 时，libutee 库中的 tee_obj 自动清理，只需清理存储 TA 中的 tee_pobj

**设计适配要点：**
- 保持 OP-TEE 的对象语义和 API 兼容性
- 使用 libutee 库管理 tee_obj，保持链表结构
- 通过 IPC 与存储 TA 通信，管理 tee_pobj
- **简化链表管理**：单链表结构，通过 TA 标识字段实现隔离
- **简化存储操作**：tee_pobj 在存储 TA 中直接调用 Trusty 存储接口，无需 fops 抽象层
- **用户对象 ID 保真**：完全以用户指定的对象 ID 为准，系统不做任何修改
- **简单 panic 清理**：遍历全局链表，匹配 TA 标识进行清理

#### 7.1.3 简化链表设计优势

**简化设计的优势：**

1. **结构简单**：单链表结构，易于理解和维护
2. **代码简洁**：减少复杂的双重链表管理代码
3. **内存效率**：无需额外的 TA 实例节点，节省内存
4. **实现简单**：查找、插入、删除操作都很直观
5. **调试友好**：单链表结构便于调试和问题定位

**简化链表结构特点：**

| 方面 | 复杂双重链表 | 简化单链表 |
|------|-------------|-----------|
| **结构复杂度** | 高（两层链表） | 低（单层链表） |
| **代码复杂度** | 高（多层管理） | 低（直接管理） |
| **内存开销** | 高（额外节点） | 低（直接存储） |
| **查找性能** | O(m) 实例内查找 | O(n) 全局查找 |
| **实现难度** | 高 | 低 |
| **维护成本** | 高 | 低 |

#### 7.1.4 存储操作简化设计优势

**取消 fops 抽象层的优势：**

1. **简化架构**：减少一层函数指针抽象，降低系统复杂度
2. **提高性能**：直接调用存储接口，减少函数调用开销
3. **降低内存占用**：每个 tee_pobj 节省一个指针字段
4. **简化维护**：减少接口适配代码，降低维护成本
5. **更好的类型安全**：直接调用具体函数，编译时类型检查更严格

**实现方式对比：**

| 方面 | OP-TEE 方式 | Trusty 简化方式 |
|------|-------------|-----------------|
| **存储操作** | `pobj->fops->read()` | `trusty_pobj_read()` |
| **函数调用层次** | 3层（API→fops→实现） | 2层（API→实现） |
| **内存占用** | 每个 pobj 8字节指针 | 节省 8字节 |
| **编译优化** | 间接调用，优化受限 | 直接调用，更好优化 |
| **调试便利性** | 需要跟踪函数指针 | 直接函数调用栈 |

#### 7.1.5 关于存储连接的设计决策

**问题分析：**
- 原设计中考虑了 `storage_session_t session` 字段和全局 `storage_service_handle`
- 在实际分析 Trusty 存储机制后发现，这些连接句柄都是不必要的
- Trusty 存储操作直接使用系统调用，无需维护连接状态

**设计决策：完全移除连接相关字段**

**理由：**
1. **Trusty 存储机制**：Trusty 存储操作是直接的系统调用，不需要连接句柄
2. **简化架构**：移除不必要的连接管理，降低系统复杂度
3. **减少资源占用**：无需维护连接状态，节省内存和管理开销
4. **提高可靠性**：减少连接相关的错误和资源泄漏风险

**最终优化结果：**
- 移除 `storage_session_t session` 字段（每个 pobj 中）
- 移除 `storage_service_handle` 字段（全局上下文中）
- 存储操作直接调用 Trusty 系统接口，如 `storage_open_file()`、`storage_read()` 等
- 大幅简化了对象初始化、操作和清理流程

#### 7.1.2 并发控制

```c
/* 并发控制最佳实践 - 基于 libutee 库 */

/* 1. libutee 库锁的获取顺序 - 避免死锁 */
void acquire_utee_locks_safely(struct trusty_tee_obj *obj1,
                               struct trusty_tee_obj *obj2) {
    /* 总是先获取全局锁，再获取对象锁 */
    mutex_acquire(&g_utee_obj_ctx.objects_lock);

    /* 按地址顺序获取对象锁，避免死锁 */
    if (obj1 < obj2) {
        mutex_acquire(&obj1->obj_lock);
        if (obj2) {
            mutex_acquire(&obj2->obj_lock);
        }
    } else {
        if (obj2) {
            mutex_acquire(&obj2->obj_lock);
        }
        mutex_acquire(&obj1->obj_lock);
    }
}

/* 2. 引用计数的原子操作 - 通过 IPC 实现 */
bool atomic_increment_pobj_refcnt(struct trusty_pobj *pobj) {
    /* 在 Trusty 中，引用计数操作通过 IPC 发送给存储 TA */
    /* 存储 TA 负责原子性地处理引用计数 */
    return utee_storage_pobj_get(pobj);
}

/* 3. busy 标志的使用 - 基于 OP-TEE 逻辑 */
TEE_Result set_object_busy_safe(struct trusty_tee_obj *obj) {
    return utee_obj_set_busy(obj);
}
```

#### 7.1.3 内存管理

```c
/* 内存管理最佳实践 - 基于 libutee 库 */

/* 1. 安全的对象分配 - 使用 libutee 库 */
struct trusty_tee_obj *safe_obj_alloc(void) {
    return utee_obj_alloc();
}

/* 2. 安全的对象释放 - 使用 libutee 库 */
void safe_obj_free(struct trusty_tee_obj *obj) {
    utee_obj_free(obj);
}

/* 3. libutee 库的内存管理 */
void utee_memory_management_best_practices(void) {
    /*
     * libutee 库内存管理要点：
     *
     * 1. 对象分配时检查资源限制
     * 2. 对象释放时安全清除敏感数据
     * 3. 使用链表统一管理所有对象
     * 4. TA 退出时自动清理所有资源
     * 5. 通过 IPC 与存储 TA 通信，避免直接内存访问
     */
}

/* 4. 安全内存清除 - 防止信息泄露（已在前面定义） */

/* 5. IPC 通信的内存管理 */
int safe_ipc_communication(handle_t handle, const void *req, size_t req_size,
                          void *resp, size_t resp_size) {
    ipc_msg_t msg;
    int ret;

    /* 准备发送消息 */
    msg.num_iov = 1;
    msg.iov[0].iov_base = (void *)req;
    msg.iov[0].iov_len = req_size;
    msg.num_handles = 0;

    ret = send_msg(handle, &msg);
    if (ret < 0) {
        return ret;
    }

    /* 接收响应 */
    ret = read_msg(handle, resp, resp_size);
    if (ret < 0) {
        /* 清除可能的敏感数据 */
        secure_memset(resp, 0, resp_size);
        return ret;
    }

    return 0;
}
```

### 7.2 兼容性考虑

#### 7.2.1 与现有系统的兼容性

**Trusty 存储服务兼容性：**
- 确保新的对象管理机制不影响现有的文件操作接口
- 保持与现有存储端口的兼容性
- 维护现有的事务处理和错误处理机制

**GP API 兼容性：**
- 完全实现 GP 标准的存储 API 接口
- 保持与 OP-TEE 的 API 兼容性
- 确保错误码和返回值的一致性

#### 7.2.2 性能影响评估

**内存使用影响：**
- 新增的对象管理结构预计增加内存使用约 1-3MB
- 通过引用计数机制减少内存占用
- 实施内存使用监控，确保不超过系统限制

**存储性能影响：**
- 基于 OP-TEE 成熟设计，性能影响最小
- 通过存储 TA 统一管理，提高缓存效率
- 实施性能基准测试，确保满足性能要求

### 7.3 设计限制说明

#### 7.3.1 多实例支持限制

**当前限制：**
- 本设计方案主要针对单实例 TA 环境
- 多实例 TA 的对象隔离和资源管理需要额外的设计考虑

**后续版本规划：**
- 多实例支持将在 v2.0 版本中单独设计和实现
- 需要扩展对象标识机制，支持实例级别的隔离

#### 7.3.2 Panic 流程限制

**当前限制：**
- 本文档暂不涉及 TA panic 时的特殊处理流程
- Panic 状态下的资源清理策略需要特殊考虑

**后续版本规划：**
- Panic 流程将在 v2.0 版本中单独设计
- 需要设计 panic 状态下的快速资源清理策略

---

**文档版本：** v1.0 - 基于 OP-TEE 设计
**最后更新：** 2024年12月
**状态：** 设计完成，待实现

**核心设计原则：**
1. **完全基于 OP-TEE 架构**：tee_obj + tee_pobj 双层对象模型
2. **libutee 库实现**：tee_obj 在 libutee 库中管理，保持链表结构
3. **IPC 通信机制**：通过 IPC 与存储 TA 通信，管理 tee_pobj
4. **GP 标准兼容**：上层提供完整的 GP 存储 API 接口
5. **简化清理机制**：TA panic 时只需清理存储 TA 中的 tee_pobj

**实现要点：**
- tee_obj 由 libutee 库管理，使用链表数据结构
- tee_pobj 由存储 TA 统一管理，基于引用计数机制
- GP API 通过 libutee 库实现，与存储 TA 进行 IPC 通信
- TA panic 时，libutee 库中的对象自动清理，存储 TA 收到通知后清理 tee_pobj

### 7.4 GP API 完整性总结

#### 7.4.1 已设计的 GP API 列表（26个）

**Generic Object Functions (5个) - ✅ 完成：**
1. `TEE_GetObjectInfo1` - 获取对象信息
2. `TEE_RestrictObjectUsage1` - 限制对象使用权限
3. `TEE_GetObjectBufferAttribute` - 获取缓冲区属性
4. `TEE_GetObjectValueAttribute` - 获取值属性
5. `TEE_CloseObject` - 关闭对象

**Transient Object Functions (7个) - ✅ 完成：**
1. `TEE_AllocateTransientObject` - 分配瞬时对象
2. `TEE_FreeTransientObject` - 释放瞬时对象
3. `TEE_ResetTransientObject` - 重置瞬时对象
4. `TEE_PopulateTransientObject` - 用属性填充瞬时对象
5. `TEE_InitRefAttribute` - 初始化引用属性
6. `TEE_InitValueAttribute` - 初始化值属性
7. `TEE_CopyObjectAttributes1` - 复制对象属性

**Persistent Object Functions (5个) - ✅ 完成：**
1. `TEE_OpenPersistentObject` - 打开持久对象
2. `TEE_CreatePersistentObject` - 创建持久对象
3. `TEE_CloseAndDeletePersistentObject1` - 关闭并删除持久对象
4. `TEE_RenamePersistentObject` - 重命名持久对象（需补充）
5. `TEE_AllocatePersistentObjectEnumerator` - 分配持久对象枚举器（需补充）

**Persistent Object Enumeration (5个) - 🔄 需补充：**
1. `TEE_FreePersistentObjectEnumerator` - 释放持久对象枚举器
2. `TEE_ResetPersistentObjectEnumerator` - 重置持久对象枚举器
3. `TEE_StartPersistentObjectEnumerator` - 启动持久对象枚举器
4. `TEE_GetNextPersistentObject` - 获取下一个持久对象

**Data Stream Access Functions (4个) - ✅ 完成：**
1. `TEE_ReadObjectData` - 读取对象数据
2. `TEE_WriteObjectData` - 写入对象数据
3. `TEE_TruncateObjectData` - 截断对象数据（需补充）
4. `TEE_SeekObjectData` - 定位对象数据（需补充）

#### 7.4.2 瞬时对象 busy 状态机制结论

**最终结论：瞬时对象在用户层 TA 管理后仍然需要 busy 状态机制。**

**关键理由：**

1. **GP 标准合规性**：GP TEE Internal Core API v1.3.1 明确要求对象操作的并发控制
2. **OP-TEE 设计一致性**：完全保持与 OP-TEE 的并发控制语义一致
3. **多线程安全保护**：防止 TA 内部多线程并发访问同一对象导致的数据竞争
4. **操作原子性保证**：确保复合操作（如属性填充、对象重置）的原子性
5. **错误处理一致性**：保持 `TEE_ERROR_BUSY` 错误码的语义一致性

**实现要点：**
- 所有对象操作前都需要检查和设置 busy 状态
- 操作完成后必须清除 busy 状态
- busy 状态检查应该是原子操作，使用互斥锁保护
- 瞬时对象和持久对象都需要 busy 状态保护

**重要设计优化：** 本设计方案相比传统 OP-TEE 架构的重要优化包括：

1. **简化链表管理**：采用简单的单链表结构，通过 TA 标识字段实现隔离，结构清晰易懂
2. **用户对象 ID 保真**：完全以用户指定的对象 ID 为准，系统不做任何修改或转换
3. **简化存储操作**：tee_pobj 在存储 TA 中直接调用 Trusty 存储接口，无需 fops 函数指针抽象层
4. **移除连接管理**：完全移除存储连接相关字段，直接使用 Trusty 系统调用，大幅简化架构

**注意：** 本文档描述的是基于 OP-TEE 设计的可信存储方案，适配 Trusty 用户空间环境。设计包含完整的 26 个 GP 存储 API，其中 21 个已完成详细设计，5 个需要在后续实现中补充。瞬时对象的 busy 状态机制是必需的，确保了系统的并发安全性和 GP 标准兼容性。简化的单链表设计和移除连接管理大幅降低了实现复杂度，同时保持了多实例支持和 panic 清理能力。
#### 7.4.3 完整 GP 存储 API 架构图

```mermaid
graph TB
    subgraph "GP API 层 (26个函数)"
        A1[Generic Object Functions<br/>5个函数]
        A2[Transient Object Functions<br/>7个函数]
        A3[Persistent Object Functions<br/>5个函数]
        A4[Persistent Object Enumeration<br/>5个函数]
        A5[Data Stream Access Functions<br/>4个函数]
    end

    subgraph "libutee 库层"
        B1[tee_obj 管理]
        B2[属性处理]
        B3[并发控制 busy状态]
        B4[IPC 通信接口]
    end

    subgraph "存储 TA 层"
        C1[tee_pobj 管理]
        C2[引用计数控制]
        C3[直接存储操作]
        C4[TA 隔离机制]
    end

    subgraph "Trusty 存储服务"
        D1[文件操作]
        D2[数据持久化]
        D3[存储空间管理]
    end

    A1 --> B1
    A2 --> B1
    A3 --> B1
    A4 --> B1
    A5 --> B1

    B1 --> B2
    B2 --> B3
    B3 --> B4

    B4 --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4

    C3 --> D1
    D1 --> D2
    D2 --> D3

    style A1 fill:#e1f5fe
    style A2 fill:#e8f5e8
    style A3 fill:#fff3e0
    style A4 fill:#fce4ec
    style A5 fill:#f3e5f5
```

#### 7.4.4 瞬时对象 busy 状态流程图

```mermaid
sequenceDiagram
    participant App as TA 应用
    participant API as GP API
    participant Obj as tee_obj
    participant Lock as busy 状态

    App->>API: 调用 GP API (如 TEE_PopulateTransientObject)
    API->>Obj: utee_obj_get(handle)
    Obj-->>API: 返回 tee_obj

    API->>Lock: utee_obj_set_busy(obj)
    alt busy 状态已设置
        Lock-->>API: 返回 TEE_ERROR_BUSY
        API-->>App: 返回 TEE_ERROR_BUSY
    else busy 状态未设置
        Lock-->>API: 设置 busy=true, 返回 TEE_SUCCESS

        API->>API: 执行实际操作<br/>(属性填充/数据操作等)

        API->>Lock: utee_obj_clear_busy(obj)
        Lock-->>API: 清除 busy=false

        API-->>App: 返回操作结果
    end
```
